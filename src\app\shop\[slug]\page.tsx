'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import {
  Store,
  Star,
  MapPin,
  Globe,
  Mail,
  Phone,
  Heart,
  Users,
  Package,
  Calendar,
  ExternalLink
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import ProductGrid from '@/components/products/ProductGrid'
import FloatingCartButton from '@/components/cart/FloatingCartButton'
import { VendorShopService } from '@/lib/services/vendorShops'
import { VendorShop, ShopProduct, ShopReview } from '@/types'
import { supabase } from '@/lib/supabase'

export default function ShopPage() {
  const params = useParams()
  const slug = params.slug as string

  const [shop, setShop] = useState<VendorShop | null>(null)
  const [products, setProducts] = useState<ShopProduct[]>([])
  const [reviews, setReviews] = useState<ShopReview[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'products' | 'reviews' | 'about'>('products')
  const [isFollowing, setIsFollowing] = useState(false)
  const [followersCount, setFollowersCount] = useState(0)
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    if (slug) {
      fetchShopData()
      checkUser()
    }
  }, [slug])

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setUser(user)
  }

  const fetchShopData = async () => {
    try {
      setLoading(true)

      // Fetch shop details
      const shopData = await VendorShopService.getShopBySlug(slug)
      if (!shopData) {
        setError('Shop not found')
        return
      }

      if (shopData.status !== 'approved') {
        setError('Shop is not available')
        return
      }

      setShop(shopData)

      // Fetch products, reviews, and followers count in parallel
      const [productsResult, reviewsResult, followersCountResult] = await Promise.all([
        VendorShopService.getShopProducts(shopData.id, 1, 12),
        VendorShopService.getShopReviews(shopData.id, 1, 5),
        VendorShopService.getShopFollowersCount(shopData.id)
      ])

      setProducts(productsResult.products)
      setReviews(reviewsResult.reviews)
      setFollowersCount(followersCountResult)

      // Check if user is following this shop
      if (user) {
        const following = await VendorShopService.isFollowingShop(shopData.id, user.id)
        setIsFollowing(following)
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load shop')
    } finally {
      setLoading(false)
    }
  }

  const handleFollowToggle = async () => {
    if (!user || !shop) {
      // Redirect to login
      window.location.href = '/auth/signin'
      return
    }

    try {
      const newFollowingState = await VendorShopService.toggleShopFollow(shop.id, user.id)
      setIsFollowing(newFollowingState)
      setFollowersCount(prev => newFollowingState ? prev + 1 : prev - 1)
    } catch (error) {
      console.error('Error toggling follow:', error)
      alert('Failed to update follow status')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="py-8 pt-28">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="animate-pulse">
              <div className="h-48 bg-gray-300 rounded-xl mb-8"></div>
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
                <div className="lg:col-span-3">
                  <div className="h-8 bg-gray-300 rounded w-1/4 mb-4"></div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {Array.from({ length: 6 }).map((_, i) => (
                      <div key={i} className="h-64 bg-gray-300 rounded-lg"></div>
                    ))}
                  </div>
                </div>
                <div className="h-96 bg-gray-300 rounded-xl"></div>
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  if (error || !shop) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="py-8 pt-28">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center py-12">
              <Store className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h1 className="text-2xl font-bold text-gray-900 mb-2">Shop Not Found</h1>
              <p className="text-gray-600">{error}</p>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="py-8 pt-28">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Shop Header */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-8">
            {/* Shop Banner */}
            <div className="relative h-48 bg-gradient-to-r from-primary-blue to-secondary-blue">
              {shop.banner_url ? (
                <img
                  src={shop.banner_url}
                  alt={`${shop.name} banner`}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center text-white">
                    <Store className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p className="text-sm opacity-75">Shop Banner</p>
                  </div>
                </div>
              )}
            </div>

            {/* Shop Info Section */}
            <div className="p-8">
              <div className="flex flex-col lg:flex-row items-start lg:items-center gap-6">
                {/* Shop Logo */}
                <div className="w-24 h-24 bg-primary-blue/10 rounded-xl flex items-center justify-center flex-shrink-0">
                  {shop.logo_url ? (
                    <img
                      src={shop.logo_url}
                      alt={shop.name}
                      className="w-full h-full object-cover rounded-xl"
                    />
                  ) : (
                    <Store className="h-12 w-12 text-primary-blue" />
                  )}
                </div>

                {/* Shop Info */}
                <div className="flex-1">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                      <h1 className="text-3xl font-bold text-gray-900 mb-2">{shop.name}</h1>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 text-yellow-400 mr-1" />
                          <span>{shop.rating.toFixed(1)} ({shop.total_reviews} reviews)</span>
                        </div>
                        <div className="flex items-center">
                          <Users className="h-4 w-4 mr-1" />
                          <span>{followersCount} followers</span>
                        </div>
                        <div className="flex items-center">
                          <Package className="h-4 w-4 mr-1" />
                          <span>{shop.total_products} products</span>
                        </div>
                      </div>
                    </div>

                    <button
                      onClick={handleFollowToggle}
                      className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
                        isFollowing
                          ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          : 'bg-primary-blue text-white hover:bg-primary-blue/90'
                      }`}
                    >
                      <Heart className={`h-4 w-4 mr-2 ${isFollowing ? 'fill-current' : ''}`} />
                      {isFollowing ? 'Following' : 'Follow'}
                    </button>
                  </div>

                  {shop.description && (
                    <p className="text-gray-600 mt-4">{shop.description}</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Tabs */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
                <div className="border-b border-gray-200">
                  <nav className="flex space-x-8 px-6">
                    {[
                      { key: 'products', label: 'Products', count: shop.total_products },
                      { key: 'reviews', label: 'Reviews', count: shop.total_reviews },
                      { key: 'about', label: 'About' }
                    ].map((tab) => (
                      <button
                        key={tab.key}
                        onClick={() => setActiveTab(tab.key as any)}
                        className={`py-4 px-1 border-b-2 font-medium text-sm ${
                          activeTab === tab.key
                            ? 'border-primary-blue text-primary-blue'
                            : 'border-transparent text-gray-500 hover:text-gray-700'
                        }`}
                      >
                        {tab.label}
                        {tab.count !== undefined && (
                          <span className="ml-2 bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
                            {tab.count}
                          </span>
                        )}
                      </button>
                    ))}
                  </nav>
                </div>

                <div className="p-6">
                  {/* Products Tab */}
                  {activeTab === 'products' && (
                    <ProductGrid
                      products={products}
                      layout="category"
                      emptyMessage="This shop hasn't added any products yet."
                      showCartButton={true}
                    />
                  )}

                  {/* Reviews Tab */}
                  {activeTab === 'reviews' && (
                    <div>
                      {reviews.length > 0 ? (
                        <div className="space-y-6">
                          {reviews.map((review) => (
                            <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                              <div className="flex items-start space-x-4">
                                <div className="w-10 h-10 bg-primary-blue/10 rounded-full flex items-center justify-center">
                                  <span className="text-sm font-medium text-primary-blue">
                                    {review.user?.full_name?.charAt(0) || 'U'}
                                  </span>
                                </div>
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2 mb-2">
                                    <span className="font-medium text-gray-900">
                                      {review.user?.full_name || 'Anonymous'}
                                    </span>
                                    <div className="flex items-center">
                                      {Array.from({ length: 5 }).map((_, i) => (
                                        <Star
                                          key={i}
                                          className={`h-4 w-4 ${
                                            i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                                          }`}
                                        />
                                      ))}
                                    </div>
                                    <span className="text-sm text-gray-500">
                                      {new Date(review.created_at).toLocaleDateString()}
                                    </span>
                                  </div>
                                  {review.review_text && (
                                    <p className="text-gray-600">{review.review_text}</p>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-12">
                          <Star className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                          <h3 className="text-lg font-medium text-gray-900 mb-2">No Reviews Yet</h3>
                          <p className="text-gray-600">Be the first to review this shop!</p>
                        </div>
                      )}
                    </div>
                  )}

                  {/* About Tab */}
                  {activeTab === 'about' && (
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">About {shop.name}</h3>
                        <p className="text-gray-600">
                          {shop.description || 'No description available.'}
                        </p>
                      </div>

                      <div>
                        <h4 className="font-medium text-gray-900 mb-3">Shop Information</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex items-center text-gray-600">
                            <Calendar className="h-4 w-4 mr-2" />
                            Joined {new Date(shop.created_at).toLocaleDateString()}
                          </div>
                          {shop.address && (
                            <div className="flex items-center text-gray-600">
                              <MapPin className="h-4 w-4 mr-2" />
                              {shop.address}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Contact Info */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="font-semibold text-gray-900 mb-4">Contact Information</h3>
                <div className="space-y-3">
                  {shop.contact_email && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Mail className="h-4 w-4 mr-3 text-gray-400" />
                      <a href={`mailto:${shop.contact_email}`} className="hover:text-primary-blue">
                        {shop.contact_email}
                      </a>
                    </div>
                  )}
                  {shop.contact_phone && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Phone className="h-4 w-4 mr-3 text-gray-400" />
                      <a href={`tel:${shop.contact_phone}`} className="hover:text-primary-blue">
                        {shop.contact_phone}
                      </a>
                    </div>
                  )}
                  {shop.website_url && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Globe className="h-4 w-4 mr-3 text-gray-400" />
                      <a
                        href={shop.website_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="hover:text-primary-blue flex items-center"
                      >
                        Visit Website
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </a>
                    </div>
                  )}
                </div>
              </div>

              {/* Shop Stats */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="font-semibold text-gray-900 mb-4">Shop Statistics</h3>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Products</span>
                    <span className="font-medium text-gray-900">{shop.total_products}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Reviews</span>
                    <span className="font-medium text-gray-900">{shop.total_reviews}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Rating</span>
                    <span className="font-medium text-gray-900">{shop.rating.toFixed(1)}/5.0</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Followers</span>
                    <span className="font-medium text-gray-900">{followersCount}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />

      {/* Floating Cart Button */}
      <FloatingCartButton />
    </div>
  )
}