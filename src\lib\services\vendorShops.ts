import { supabase, TABLES } from '@/lib/supabase'
import { VendorShop, ShopProduct, ShopReview, ShopFollower } from '@/types'

/**
 * VendorShopService - Completely rebuilt with proper error handling and relationships
 *
 * Key improvements:
 * - Uses correct foreign key constraint names from migration files
 * - Comprehensive error handling and logging
 * - Proper validation for all operations
 * - Optimized queries with proper relationships
 */
export class VendorShopService {
  /**
   * Create a new vendor shop with comprehensive validation
   */
  static async createShop(shopData: Omit<VendorShop, 'id' | 'created_at' | 'updated_at' | 'rating' | 'total_reviews' | 'total_products' | 'total_sales'>): Promise<VendorShop> {
    try {
      if (!shopData.user_id) {
        throw new Error('User ID is required')
      }

      if (!shopData.name?.trim()) {
        throw new Error('Shop name is required')
      }

      if (!shopData.description?.trim()) {
        throw new Error('Shop description is required')
      }

      // Check if user already has a shop
      const existingUserShop = await this.getUserShop(shopData.user_id)
      if (existingUserShop) {
        throw new Error('You can only create one shop per account. You already have a shop.')
      }

      console.log(`Creating shop for user: ${shopData.user_id}, name: ${shopData.name}`)

      // Generate slug from name
      const slug = shopData.name.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '')

      // Check if slug already exists
      const { data: existingSlugShop } = await supabase
        .from(TABLES.VENDOR_SHOPS)
        .select('id')
        .eq('slug', `${slug}-${Date.now()}`)
        .single()

      const finalSlug = existingSlugShop ? `${slug}-${Date.now()}-${Math.random().toString(36).substr(2, 5)}` : `${slug}-${Date.now()}`

      const { data, error } = await supabase
        .from(TABLES.VENDOR_SHOPS)
        .insert({
          ...shopData,
          name: shopData.name.trim(),
          description: shopData.description.trim(),
          slug: finalSlug,
          status: 'pending',
          is_featured: false,
          rating: 0,
          total_reviews: 0,
          total_products: 0,
          total_sales: 0
        })
        .select(`
          *,
          user:users!vendor_shops_user_id_fkey(id, full_name, email, avatar_url)
        `)
        .single()

      if (error) {
        console.error('Error creating shop:', error)
        throw new Error(`Failed to create shop: ${error.message}`)
      }

      if (!data) {
        throw new Error('Shop was not created')
      }

      console.log('Shop created successfully:', data.id)
      return data
    } catch (error) {
      console.error('VendorShopService.createShop error:', error)
      throw error
    }
  }

  /**
   * Get shop by slug with comprehensive error handling
   */
  static async getShopBySlug(slug: string): Promise<VendorShop | null> {
    try {
      if (!slug?.trim()) {
        throw new Error('Shop slug is required')
      }

      console.log(`Fetching shop by slug: ${slug}`)

      const { data, error } = await supabase
        .from(TABLES.VENDOR_SHOPS)
        .select(`
          *,
          user:users!vendor_shops_user_id_fkey(id, full_name, email, avatar_url)
        `)
        .eq('slug', slug.trim())
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          console.log(`Shop not found with slug: ${slug}`)
          return null // Not found
        }
        console.error('Error fetching shop by slug:', error)
        throw new Error(`Failed to fetch shop: ${error.message}`)
      }

      if (data) {
        console.log(`Found shop: ${data.id} with slug: ${slug}`)
      }

      return data
    } catch (error) {
      console.error('VendorShopService.getShopBySlug error:', error)
      throw error
    }
  }

  /**
   * Get user's shop with comprehensive error handling
   */
  static async getUserShop(userId: string): Promise<VendorShop | null> {
    try {
      if (!userId) {
        throw new Error('User ID is required')
      }

      console.log(`Fetching shop for user: ${userId}`)

      const { data, error } = await supabase
        .from(TABLES.VENDOR_SHOPS)
        .select(`
          *,
          user:users!vendor_shops_user_id_fkey(id, full_name, email, avatar_url)
        `)
        .eq('user_id', userId)
        .single()

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
        console.error('Error fetching user shop:', error)
        throw new Error(`Failed to fetch user shop: ${error.message}`)
      }

      if (data) {
        console.log(`Found shop for user ${userId}: ${data.id}`)
      } else {
        console.log(`No shop found for user: ${userId}`)
      }

      return data || null
    } catch (error) {
      console.error('VendorShopService.getUserShop error:', error)
      throw error
    }
  }

  /**
   * Get all approved shops with pagination and comprehensive error handling
   */
  static async getApprovedShops(page: number = 1, limit: number = 20): Promise<{
    shops: VendorShop[]
    total: number
  }> {
    try {
      const offset = (page - 1) * limit

      console.log(`Fetching approved shops - page: ${page}, limit: ${limit}`)

      const [dataResult, countResult] = await Promise.all([
        supabase
          .from(TABLES.VENDOR_SHOPS)
          .select(`
            *,
            user:users!vendor_shops_user_id_fkey(id, full_name, email, avatar_url)
          `)
          .eq('status', 'approved')
          .order('is_featured', { ascending: false })
          .order('created_at', { ascending: false })
          .range(offset, offset + limit - 1),
        supabase
          .from(TABLES.VENDOR_SHOPS)
          .select('*', { count: 'exact', head: true })
          .eq('status', 'approved')
      ])

      if (dataResult.error) {
        console.error('Error fetching approved shops:', dataResult.error)
        throw new Error(`Failed to fetch shops: ${dataResult.error.message}`)
      }

      if (countResult.error) {
        console.error('Error fetching approved shops count:', countResult.error)
        throw new Error(`Failed to fetch shops count: ${countResult.error.message}`)
      }

      console.log(`Successfully fetched ${dataResult.data?.length || 0} approved shops (total: ${countResult.count || 0})`)

      return {
        shops: dataResult.data || [],
        total: countResult.count || 0
      }
    } catch (error) {
      console.error('VendorShopService.getApprovedShops error:', error)
      throw error
    }
  }

  /**
   * Get shops with filters and comprehensive error handling
   */
  static async getShops(
    filters: {
      status?: 'pending' | 'approved' | 'rejected' | 'suspended'
      featured?: boolean
      category?: string
      location?: string
      search?: string
    } = {},
    page: number = 1,
    limit: number = 20
  ): Promise<{
    shops: VendorShop[]
    total: number
  }> {
    try {
      const offset = (page - 1) * limit

      console.log(`Fetching shops with filters:`, filters, `page: ${page}, limit: ${limit}`)

      let query = supabase
        .from(TABLES.VENDOR_SHOPS)
        .select(`
          *,
          user:users!vendor_shops_user_id_fkey(id, full_name, email, avatar_url)
        `)
        .order('is_featured', { ascending: false })
        .order('created_at', { ascending: false })

      let countQuery = supabase
        .from(TABLES.VENDOR_SHOPS)
        .select('*', { count: 'exact', head: true })

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status)
        countQuery = countQuery.eq('status', filters.status)
      }

      if (filters.featured !== undefined) {
        query = query.eq('is_featured', filters.featured)
        countQuery = countQuery.eq('is_featured', filters.featured)
      }

      if (filters.search?.trim()) {
        const searchTerm = `%${filters.search.trim()}%`
        query = query.or(`name.ilike.${searchTerm},description.ilike.${searchTerm}`)
        countQuery = countQuery.or(`name.ilike.${searchTerm},description.ilike.${searchTerm}`)
      }

      if (filters.location?.trim()) {
        query = query.ilike('address', `%${filters.location.trim()}%`)
        countQuery = countQuery.ilike('address', `%${filters.location.trim()}%`)
      }

      const [dataResult, countResult] = await Promise.all([
        query.range(offset, offset + limit - 1),
        countQuery
      ])

      if (dataResult.error) {
        console.error('Error fetching shops:', dataResult.error)
        throw new Error(`Failed to fetch shops: ${dataResult.error.message}`)
      }

      if (countResult.error) {
        console.error('Error fetching shops count:', countResult.error)
        throw new Error(`Failed to fetch shops count: ${countResult.error.message}`)
      }

      console.log(`Successfully fetched ${dataResult.data?.length || 0} shops (total: ${countResult.count || 0})`)

      return {
        shops: dataResult.data || [],
        total: countResult.count || 0
      }
    } catch (error) {
      console.error('VendorShopService.getShops error:', error)
      throw error
    }
  }

  /**
   * Get user's shops with comprehensive error handling
   */
  static async getUserShops(userId: string): Promise<VendorShop[]> {
    try {
      if (!userId) {
        throw new Error('User ID is required')
      }

      console.log(`Fetching shops for user: ${userId}`)

      const { data, error } = await supabase
        .from(TABLES.VENDOR_SHOPS)
        .select(`
          *,
          user:users!vendor_shops_user_id_fkey(id, full_name, email, avatar_url)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching user shops:', error)
        throw new Error(`Failed to fetch user shops: ${error.message}`)
      }

      console.log(`Successfully fetched ${data?.length || 0} shops for user: ${userId}`)

      return data || []
    } catch (error) {
      console.error('VendorShopService.getUserShops error:', error)
      throw error
    }
  }

  /**
   * Update shop
   */
  static async updateShop(shopId: string, updates: Partial<VendorShop>): Promise<VendorShop> {
    const { data, error } = await supabase
      .from(TABLES.VENDOR_SHOPS)
      .update(updates)
      .eq('id', shopId)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update shop: ${error.message}`)
    }

    return data
  }

  /**
   * Refresh all shop statistics for all shops
   */
  static async refreshAllShopStatistics(): Promise<void> {
    try {
      console.log('Refreshing statistics for all shops...')

      // Get all approved shops
      const { data: shops, error } = await supabase
        .from(TABLES.VENDOR_SHOPS)
        .select('id, name')
        .eq('status', 'approved')

      if (error) {
        throw new Error(`Failed to fetch shops: ${error.message}`)
      }

      if (!shops || shops.length === 0) {
        console.log('No approved shops found')
        return
      }

      // Refresh statistics for each shop
      for (const shop of shops) {
        try {
          await this.refreshShopStatistics(shop.id)
          console.log(`✓ Updated statistics for shop: ${shop.name}`)
        } catch (error) {
          console.error(`✗ Failed to update statistics for shop ${shop.name}:`, error)
        }
      }

      console.log('Completed refreshing statistics for all shops')
    } catch (error) {
      console.error('Error refreshing all shop statistics:', error)
      throw error
    }
  }

  /**
   * Manually refresh shop statistics (products, reviews, rating, sales)
   */
  static async refreshShopStatistics(shopId: string): Promise<VendorShop> {
    try {
      console.log(`Refreshing statistics for shop: ${shopId}`)

      // First get the product IDs for this shop
      const { data: shopProducts, error: productsError } = await supabase
        .from(TABLES.SHOP_PRODUCTS)
        .select('id')
        .eq('shop_id', shopId)

      if (productsError) {
        console.error('Error fetching shop products:', productsError)
        throw new Error(`Failed to fetch shop products: ${productsError.message}`)
      }

      const productIds = shopProducts?.map(p => p.id) || []

      // Get current statistics from related tables with individual error handling
      const [productsResult, reviewsResult, ordersResult, viewsResult] = await Promise.allSettled([
        // Count products
        supabase
          .from(TABLES.SHOP_PRODUCTS)
          .select('*', { count: 'exact', head: true })
          .eq('shop_id', shopId),

        // Get product reviews for rating calculation
        productIds.length > 0
          ? supabase
              .from(TABLES.PRODUCT_REVIEWS)
              .select('rating')
              .in('product_id', productIds)
          : Promise.resolve({ data: [], error: null }),

        // Count completed sales
        supabase
          .from(TABLES.SHOP_ORDERS)
          .select('*', { count: 'exact', head: true })
          .eq('shop_id', shopId)
          .in('status', ['delivered', 'completed']),

        // Calculate total views from all products
        supabase
          .from(TABLES.SHOP_PRODUCTS)
          .select('views')
          .eq('shop_id', shopId)
      ])

      // Process results with error handling
      const totalProducts = productsResult.status === 'fulfilled' && !productsResult.value.error
        ? productsResult.value.count || 0
        : 0

      const reviews = reviewsResult.status === 'fulfilled' && !reviewsResult.value.error
        ? reviewsResult.value.data || []
        : []
      const totalReviews = reviews.length
      const averageRating = totalReviews > 0
        ? reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews
        : 0

      const totalSales = ordersResult.status === 'fulfilled' && !ordersResult.value.error
        ? ordersResult.value.count || 0
        : 0

      const productViews = viewsResult.status === 'fulfilled' && !viewsResult.value.error
        ? viewsResult.value.data || []
        : []
      const totalViews = productViews.reduce((sum, product) => sum + (product.views || 0), 0)

      // Log any errors from the queries
      if (productsResult.status === 'rejected') {
        console.warn('Failed to get products count:', productsResult.reason)
      }
      if (reviewsResult.status === 'rejected') {
        console.warn('Failed to get reviews:', reviewsResult.reason)
      }
      if (ordersResult.status === 'rejected') {
        console.warn('Failed to get orders count:', ordersResult.reason)
      }
      if (viewsResult.status === 'rejected') {
        console.warn('Failed to get product views:', viewsResult.reason)
      }

      console.log(`Shop ${shopId} statistics:`, {
        totalProducts,
        totalReviews,
        averageRating,
        totalSales,
        totalViews
      })

      // Update the shop with calculated statistics
      const { data, error } = await supabase
        .from(TABLES.VENDOR_SHOPS)
        .update({
          total_products: totalProducts,
          total_reviews: totalReviews,
          rating: Number(averageRating.toFixed(2)),
          total_sales: totalSales,
          total_views: totalViews,
          updated_at: new Date().toISOString()
        })
        .eq('id', shopId)
        .select()

      if (error) {
        throw new Error(`Failed to update shop statistics: ${error.message}`)
      }

      if (!data || data.length === 0) {
        throw new Error(`Shop not found: ${shopId}`)
      }

      console.log(`Successfully updated statistics for shop: ${shopId}`)
      return data[0]
    } catch (error) {
      console.error('Error refreshing shop statistics:', error)
      throw error
    }
  }

  /**
   * Add product to shop
   */
  static async addProductToShop(productData: Omit<ShopProduct, 'id' | 'created_at' | 'updated_at'>): Promise<ShopProduct> {
    const { data, error } = await supabase
      .from(TABLES.SHOP_PRODUCTS)
      .insert(productData)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to add product to shop: ${error.message}`)
    }

    return data
  }

  /**
   * Get shop products - delegated to ShopProductService
   */
  static async getShopProducts(shopId: string, page: number = 1, limit: number = 20): Promise<{
    products: ShopProduct[]
    total: number
  }> {
    // Import ShopProductService dynamically to avoid circular imports
    const { ShopProductService } = await import('./shopProducts')
    return ShopProductService.getShopProducts(shopId, page, limit)
  }

  /**
   * Add review to shop
   */
  static async addShopReview(reviewData: Omit<ShopReview, 'id' | 'created_at' | 'updated_at'>): Promise<ShopReview> {
    const { data, error } = await supabase
      .from(TABLES.SHOP_REVIEWS)
      .insert(reviewData)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to add review: ${error.message}`)
    }

    return data
  }

  /**
   * Get shop reviews
   */
  static async getShopReviews(shopId: string, page: number = 1, limit: number = 10): Promise<{
    reviews: ShopReview[]
    total: number
  }> {
    const offset = (page - 1) * limit

    const [dataResult, countResult] = await Promise.all([
      supabase
        .from(TABLES.SHOP_REVIEWS)
        .select(`
          *,
          user:${TABLES.USERS}!shop_reviews_user_id_fkey(id, full_name, avatar_url)
        `)
        .eq('shop_id', shopId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1),
      supabase
        .from(TABLES.SHOP_REVIEWS)
        .select('*', { count: 'exact', head: true })
        .eq('shop_id', shopId)
    ])

    if (dataResult.error) {
      throw new Error(`Failed to fetch reviews: ${dataResult.error.message}`)
    }

    return {
      reviews: dataResult.data || [],
      total: countResult.count || 0
    }
  }

  /**
   * Follow/unfollow shop
   */
  static async toggleShopFollow(shopId: string, userId: string): Promise<boolean> {
    // Check if already following
    const { data: existing } = await supabase
      .from(TABLES.SHOP_FOLLOWERS)
      .select('id')
      .eq('shop_id', shopId)
      .eq('user_id', userId)
      .single()

    if (existing) {
      // Unfollow
      const { error } = await supabase
        .from(TABLES.SHOP_FOLLOWERS)
        .delete()
        .eq('shop_id', shopId)
        .eq('user_id', userId)

      if (error) {
        throw new Error(`Failed to unfollow shop: ${error.message}`)
      }
      return false
    } else {
      // Follow
      const { error } = await supabase
        .from(TABLES.SHOP_FOLLOWERS)
        .insert({ shop_id: shopId, user_id: userId })

      if (error) {
        throw new Error(`Failed to follow shop: ${error.message}`)
      }
      return true
    }
  }

  /**
   * Check if user is following shop
   */
  static async isFollowingShop(shopId: string, userId: string): Promise<boolean> {
    const { data, error } = await supabase
      .from(TABLES.SHOP_FOLLOWERS)
      .select('id')
      .eq('shop_id', shopId)
      .eq('user_id', userId)
      .single()

    return !error && !!data
  }

  /**
   * Get shop followers count
   */
  static async getShopFollowersCount(shopId: string): Promise<number> {
    try {
      const { count, error } = await supabase
        .from(TABLES.SHOP_FOLLOWERS)
        .select('*', { count: 'exact', head: true })
        .eq('shop_id', shopId)

      if (error) {
        console.warn(`Failed to get followers count for shop ${shopId}:`, error.message)
        return 0
      }

      return count || 0
    } catch (error) {
      console.warn(`Error getting followers count for shop ${shopId}:`, error)
      return 0
    }
  }

  /**
   * Search shops
   */
  static async searchShops(query: string, page: number = 1, limit: number = 20): Promise<{
    shops: VendorShop[]
    total: number
  }> {
    const offset = (page - 1) * limit
    const searchTerm = `%${query}%`

    const [dataResult, countResult] = await Promise.all([
      supabase
        .from(TABLES.VENDOR_SHOPS)
        .select(`
          *,
          user:${TABLES.USERS}!vendor_shops_user_id_fkey(id, full_name, email, avatar_url)
        `)
        .eq('status', 'approved')
        .or(`name.ilike.${searchTerm},description.ilike.${searchTerm}`)
        .order('is_featured', { ascending: false })
        .order('rating', { ascending: false })
        .range(offset, offset + limit - 1),
      supabase
        .from(TABLES.VENDOR_SHOPS)
        .select('*', { count: 'exact', head: true })
        .eq('status', 'approved')
        .or(`name.ilike.${searchTerm},description.ilike.${searchTerm}`)
    ])

    if (dataResult.error) {
      throw new Error(`Failed to search shops: ${dataResult.error.message}`)
    }

    return {
      shops: dataResult.data || [],
      total: countResult.count || 0
    }
  }
}
