import { NextRequest, NextResponse } from 'next/server'
import { VendorShopService } from '@/lib/services/vendorShops'
import { createClient } from '@supabase/supabase-js'

// Create admin Supabase client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}))
    const { shopId } = body

    if (shopId) {
      // Refresh specific shop statistics using admin client
      console.log(`Refreshing statistics for specific shop: ${shopId}`)
      const result = await refreshShopStatisticsAdmin(shopId)
      console.log('Shop refresh result:', result)

      return NextResponse.json({
        success: true,
        message: `Shop statistics refreshed successfully for shop: ${shopId}`,
        data: result
      })
    } else {
      // Refresh all shop statistics using admin client
      console.log('Refreshing statistics for all shops')
      await refreshAllShopStatisticsAdmin()

      return NextResponse.json({
        success: true,
        message: 'Shop statistics refreshed successfully for all shops'
      })
    }
  } catch (error) {
    console.error('Error refreshing shop statistics:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to refresh shop statistics'
      },
      { status: 500 }
    )
  }
}

// Admin version of refreshShopStatistics that uses service role
async function refreshShopStatisticsAdmin(shopId: string) {
  try {
    console.log(`Refreshing statistics for shop: ${shopId}`)

    // First get the product IDs for this shop
    const { data: shopProducts, error: productsError } = await supabaseAdmin
      .from('shop_products')
      .select('id')
      .eq('shop_id', shopId)

    if (productsError) {
      console.error('Error fetching shop products:', productsError)
      throw new Error(`Failed to fetch shop products: ${productsError.message}`)
    }

    const productIds = shopProducts?.map(p => p.id) || []

    // Get current statistics from related tables with individual error handling
    const [productsResult, reviewsResult, ordersResult, viewsResult] = await Promise.allSettled([
      // Count products
      supabaseAdmin
        .from('shop_products')
        .select('*', { count: 'exact', head: true })
        .eq('shop_id', shopId),

      // Get product reviews for rating calculation
      productIds.length > 0
        ? supabaseAdmin
            .from('product_reviews')
            .select('rating')
            .in('product_id', productIds)
        : Promise.resolve({ data: [], error: null }),

      // Count completed sales
      supabaseAdmin
        .from('shop_orders')
        .select('*', { count: 'exact', head: true })
        .eq('shop_id', shopId)
        .in('status', ['delivered', 'completed']),

      // Calculate total views from all products
      supabaseAdmin
        .from('shop_products')
        .select('views')
        .eq('shop_id', shopId)
    ])

    // Process results with error handling
    const totalProducts = productsResult.status === 'fulfilled' && !productsResult.value.error
      ? productsResult.value.count || 0
      : 0

    const reviews = reviewsResult.status === 'fulfilled' && !reviewsResult.value.error
      ? reviewsResult.value.data || []
      : []
    const totalReviews = reviews.length
    const averageRating = totalReviews > 0
      ? reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews
      : 0

    const totalSales = ordersResult.status === 'fulfilled' && !ordersResult.value.error
      ? ordersResult.value.count || 0
      : 0

    const productViews = viewsResult.status === 'fulfilled' && !viewsResult.value.error
      ? viewsResult.value.data || []
      : []
    const totalViews = productViews.reduce((sum, product) => sum + (product.views || 0), 0)

    console.log(`Shop ${shopId} statistics:`, {
      totalProducts,
      totalReviews,
      averageRating,
      totalSales,
      totalViews
    })

    // Update the shop with calculated statistics using admin client
    const { data, error } = await supabaseAdmin
      .from('vendor_shops')
      .update({
        total_products: totalProducts,
        total_reviews: totalReviews,
        rating: Number(averageRating.toFixed(2)),
        total_sales: totalSales,
        total_views: totalViews,
        updated_at: new Date().toISOString()
      })
      .eq('id', shopId)
      .select()

    if (error) {
      throw new Error(`Failed to update shop statistics: ${error.message}`)
    }

    if (!data || data.length === 0) {
      throw new Error(`Shop not found: ${shopId}`)
    }

    console.log(`Successfully updated statistics for shop: ${shopId}`)
    return data[0]
  } catch (error) {
    console.error('Error refreshing shop statistics:', error)
    throw error
  }
}

// Admin version of refreshAllShopStatistics that uses service role
async function refreshAllShopStatisticsAdmin() {
  try {
    console.log('Refreshing statistics for all shops...')

    // Get all approved shops
    const { data: shops, error } = await supabaseAdmin
      .from('vendor_shops')
      .select('id, name')
      .eq('status', 'approved')

    if (error) {
      throw new Error(`Failed to fetch shops: ${error.message}`)
    }

    if (!shops || shops.length === 0) {
      console.log('No approved shops found')
      return
    }

    // Refresh statistics for each shop
    for (const shop of shops) {
      try {
        await refreshShopStatisticsAdmin(shop.id)
        console.log(`✓ Updated statistics for shop: ${shop.name}`)
      } catch (error) {
        console.error(`✗ Failed to update statistics for shop ${shop.name}:`, error)
      }
    }

    console.log('Completed refreshing statistics for all shops')
  } catch (error) {
    console.error('Error refreshing all shop statistics:', error)
    throw error
  }
}
