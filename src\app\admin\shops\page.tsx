'use client'

import { useState, useEffect, Suspense, useRef } from 'react'
import { useSearchParams } from 'next/navigation'
import {
  Store,
  Search,
  MoreVertical,
  CheckCircle,
  XCircle,
  Clock,
  Star,
  Users,
  Package,
  Trash2,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Eye,
  Edit,
  TrendingUp,
  DollarSign,
  ShoppingCart,
  BarChart3,
  Settings,
  Shield,
  AlertTriangle,
  Filter,
  Download
} from 'lucide-react'
import AdminLayout from '@/components/admin/AdminLayout'
import { AdminService } from '@/lib/services/admin'
import { ShopProductService } from '@/lib/services/shopProducts'
import { ShopAnalyticsService, ShopAnalytics } from '@/lib/services/shopAnalytics'
import { OrderService } from '@/lib/services/orders'
import { VendorShop, ShopProduct, ShopOrder } from '@/types'
import { showConfirmation } from '@/components/ui/ConfirmationDialog'
import { useAlert } from '@/contexts/AlertContext'
import { formatCurrency } from '@/lib/utils'

// ShopAnalytics interface is now imported from the service

interface ShopActionsProps {
  shop: VendorShop
  onStatusUpdate: (shopId: string, status: 'approved' | 'rejected' | 'suspended') => void
  onToggleFeatured: (shopId: string, isFeatured: boolean) => void
  onDelete: (shopId: string) => void
  onViewDetails: (shopId: string) => void
}

function ShopActions({ shop, onStatusUpdate, onToggleFeatured, onDelete, onViewDetails }: ShopActionsProps) {
  const [showMenu, setShowMenu] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => setShowMenu(!showMenu)}
        className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
      >
        <MoreVertical className="h-4 w-4" />
      </button>
      
      {showMenu && (
        <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          <div className="py-1">
            <button
              onClick={() => {
                onViewDetails(shop.id)
                setShowMenu(false)
              }}
              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
            >
              <Eye className="h-4 w-4 mr-2" />
              View Details
            </button>

            <div className="border-t border-gray-100 my-1"></div>

            {shop.status === 'pending' && (
              <>
                <button
                  onClick={() => {
                    onStatusUpdate(shop.id, 'approved')
                    setShowMenu(false)
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-green-600 hover:bg-green-50"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve Shop
                </button>
                <button
                  onClick={() => {
                    onStatusUpdate(shop.id, 'rejected')
                    setShowMenu(false)
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject Shop
                </button>
              </>
            )}
            {shop.status === 'approved' && (
              <>
                <button
                  onClick={() => {
                    onToggleFeatured(shop.id, !shop.is_featured)
                    setShowMenu(false)
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-blue-600 hover:bg-blue-50"
                >
                  <Star className="h-4 w-4 mr-2" />
                  {shop.is_featured ? 'Remove Featured' : 'Make Featured'}
                </button>
                <button
                  onClick={() => {
                    onStatusUpdate(shop.id, 'suspended')
                    setShowMenu(false)
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-orange-600 hover:bg-orange-50"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Suspend Shop
                </button>
              </>
            )}
            {shop.status === 'suspended' && (
              <button
                onClick={() => {
                  onStatusUpdate(shop.id, 'approved')
                  setShowMenu(false)
                }}
                className="flex items-center w-full px-4 py-2 text-sm text-green-600 hover:bg-green-50"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Reactivate Shop
              </button>
            )}
            <button
              onClick={async () => {
                const confirmed = await showConfirmation({
                  title: 'Delete Shop',
                  message: 'Are you sure you want to delete this shop? This action cannot be undone.',
                  confirmText: 'Delete',
                  cancelText: 'Cancel',
                  variant: 'danger'
                })

                if (confirmed) {
                  onDelete(shop.id)
                }
                setShowMenu(false)
              }}
              className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Shop
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

function AdminShopsContent() {
  const searchParams = useSearchParams()
  const initialStatus = searchParams.get('status') as 'pending' | 'approved' | 'rejected' | 'suspended' | null
  const { showAlert } = useAlert()

  const [shops, setShops] = useState<VendorShop[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalShops, setTotalShops] = useState(0)
  const [selectedStatus, setSelectedStatus] = useState<string>(initialStatus || 'all')
  const [selectedShop, setSelectedShop] = useState<VendorShop | null>(null)
  const [shopProducts, setShopProducts] = useState<ShopProduct[]>([])
  const [shopAnalytics, setShopAnalytics] = useState<ShopAnalytics | null>(null)
  const [shopOrders, setShopOrders] = useState<ShopOrder[]>([])
  const [showShopDetails, setShowShopDetails] = useState(false)
  const [loadingShopDetails, setLoadingShopDetails] = useState(false)
  const [activeTab, setActiveTab] = useState<'analytics' | 'products' | 'orders'>('analytics')

  const shopsPerPage = 20

  useEffect(() => {
    fetchShops()
  }, [currentPage, selectedStatus])

  const fetchShops = async () => {
    try {
      setLoading(true)
      setError(null)
      const status = selectedStatus === 'all' ? undefined : selectedStatus as any
      const { shops: shopsData, total } = await AdminService.getAllShops(status, currentPage, shopsPerPage)
      setShops(shopsData || [])
      setTotalShops(total || 0)
    } catch (err) {
      console.error('Error fetching shops:', err)
      setError(err instanceof Error ? err.message : 'Failed to load shops')
      setShops([])
      setTotalShops(0)
    } finally {
      setLoading(false)
    }
  }

  const handleViewShopDetails = async (shopId: string) => {
    try {
      setLoadingShopDetails(true)
      const shop = shops.find(s => s.id === shopId)
      if (!shop) return

      setSelectedShop(shop)

      // Fetch shop products
      const { products } = await ShopProductService.getShopProducts(shopId, 1, 50)
      setShopProducts(products)

      // Fetch real analytics data
      const analytics = await ShopAnalyticsService.getShopAnalytics(shopId)
      setShopAnalytics(analytics)

      // Fetch shop orders
      const { orders } = await OrderService.getSellerOrders(shop.user_id, shopId, 1, 50)
      setShopOrders(orders)

      setShowShopDetails(true)
    } catch (error) {
      console.error('Error fetching shop details:', error)
      await showAlert({
        title: 'Error',
        message: 'Failed to load shop details',
        variant: 'danger'
      })
    } finally {
      setLoadingShopDetails(false)
    }
  }

  const handleToggleProductStatus = async (productId: string, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active'
      await ShopProductService.updateProduct(productId, { status: newStatus })

      // Update local state
      setShopProducts(prev => prev.map(p =>
        p.id === productId ? { ...p, status: newStatus } : p
      ))

      await showAlert({
        title: 'Success',
        message: `Product ${newStatus === 'active' ? 'enabled' : 'disabled'} successfully!`,
        variant: 'success'
      })
    } catch (error) {
      await showAlert({
        title: 'Error',
        message: 'Failed to update product status',
        variant: 'danger'
      })
    }
  }

  const handleStatusUpdate = async (shopId: string, status: 'approved' | 'rejected' | 'suspended') => {
    try {
      console.log(`Updating shop ${shopId} status to ${status}`)
      await AdminService.updateShopStatus(shopId, status)
      console.log(`Shop ${shopId} status updated successfully`)
      await fetchShops() // Refresh the list
    } catch (err) {
      console.error('Error updating shop status:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to update shop status'
      alert(`Error: ${errorMessage}`)
    }
  }

  const handleToggleFeatured = async (shopId: string, isFeatured: boolean) => {
    try {
      console.log(`Toggling featured status for shop ${shopId} to ${isFeatured}`)
      await AdminService.toggleShopFeatured(shopId, isFeatured)
      console.log(`Featured status updated successfully`)
      await fetchShops() // Refresh the list
    } catch (err) {
      console.error('Error updating featured status:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to update featured status'
      alert(`Error: ${errorMessage}`)
    }
  }

  const handleDelete = async (shopId: string) => {
    try {
      console.log(`Deleting shop ${shopId}`)
      await AdminService.deleteShop(shopId)
      console.log(`Shop deleted successfully`)
      await fetchShops() // Refresh the list
    } catch (err) {
      console.error('Error deleting shop:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete shop'
      alert(`Error: ${errorMessage}`)
    }
  }

  const filteredShops = shops.filter(shop => 
    shop.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    shop.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    shop.user?.full_name?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const totalPages = Math.ceil(totalShops / shopsPerPage)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-orange-100 text-orange-800'
      case 'rejected': return 'bg-red-100 text-red-800'
      case 'suspended': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return CheckCircle
      case 'pending': return Clock
      case 'rejected': return XCircle
      case 'suspended': return XCircle
      default: return Store
    }
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-1/4 mb-6"></div>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="h-10 bg-gray-300 rounded w-1/3"></div>
            </div>
            <div className="divide-y divide-gray-200">
              {Array.from({ length: 10 }).map((_, i) => (
                <div key={i} className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="h-16 w-16 bg-gray-300 rounded-lg"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-300 rounded w-1/3 mb-2"></div>
                      <div className="h-3 bg-gray-300 rounded w-1/2 mb-2"></div>
                      <div className="h-3 bg-gray-300 rounded w-1/4"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Vendor Shops Management</h1>
            <p className="text-gray-600">Review and manage vendor shops</p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Store className="h-4 w-4" />
            <span>{totalShops} total shops</span>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search shops by name, description, or owner..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={selectedStatus}
                onChange={(e) => {
                  setSelectedStatus(e.target.value)
                  setCurrentPage(1)
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
                <option value="suspended">Suspended</option>
              </select>
            </div>
          </div>
        </div>

        {/* Shops List */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          {error ? (
            <div className="text-center py-12">
              <div className="text-red-600 mb-4">{error}</div>
              <button
                onClick={fetchShops}
                className="px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90"
              >
                Try Again
              </button>
            </div>
          ) : filteredShops.length === 0 ? (
            <div className="text-center py-12">
              <div className="flex flex-col items-center">
                <Store className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No shops found</h3>
                <p className="text-gray-500">
                  {searchTerm ? 'Try adjusting your search terms.' : 'No vendor shops have been created yet.'}
                </p>
              </div>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredShops.map((shop) => {
              const StatusIcon = getStatusIcon(shop.status)
              return (
                <div key={shop.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-start space-x-4">
                    {/* Shop Logo */}
                    <div className="h-16 w-16 bg-gray-200 rounded-lg flex-shrink-0 overflow-hidden">
                      {shop.logo_url ? (
                        <img
                          src={shop.logo_url}
                          alt={shop.name}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="h-full w-full flex items-center justify-center">
                          <Store className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                    </div>

                    {/* Shop Details */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h3 className="text-lg font-medium text-gray-900 truncate">
                              {shop.name}
                            </h3>
                            {shop.is_featured && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <Star className="h-3 w-3 mr-1" />
                                Featured
                              </span>
                            )}
                          </div>
                          
                          {shop.description && (
                            <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                              {shop.description}
                            </p>
                          )}
                          
                          <div className="flex items-center space-x-4 text-sm text-gray-500 mb-2">
                            <div className="flex items-center">
                              <Star className="h-3 w-3 mr-1" />
                              {shop.rating.toFixed(1)} ({shop.total_reviews} reviews)
                            </div>
                            <div className="flex items-center">
                              <Package className="h-3 w-3 mr-1" />
                              {shop.total_products} products
                            </div>
                            <div className="flex items-center">
                              <Calendar className="h-3 w-3 mr-1" />
                              {new Date(shop.created_at).toLocaleDateString()}
                            </div>
                          </div>

                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">
                              Owner: {shop.user?.full_name || 'Unknown'}
                            </span>
                            {shop.contact_email && (
                              <>
                                <span className="text-gray-300">•</span>
                                <div className="flex items-center text-sm text-gray-600">
                                  <Mail className="h-3 w-3 mr-1" />
                                  {shop.contact_email}
                                </div>
                              </>
                            )}
                            {shop.contact_phone && (
                              <>
                                <span className="text-gray-300">•</span>
                                <div className="flex items-center text-sm text-gray-600">
                                  <Phone className="h-3 w-3 mr-1" />
                                  {shop.contact_phone}
                                </div>
                              </>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center space-x-3 ml-4">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(shop.status)}`}>
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {shop.status.charAt(0).toUpperCase() + shop.status.slice(1)}
                          </span>
                          <ShopActions
                            shop={shop}
                            onStatusUpdate={handleStatusUpdate}
                            onToggleFeatured={handleToggleFeatured}
                            onDelete={handleDelete}
                            onViewDetails={handleViewShopDetails}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {((currentPage - 1) * shopsPerPage) + 1} to {Math.min(currentPage * shopsPerPage, totalShops)} of {totalShops} shops
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Shop Details Modal */}
      {showShopDetails && selectedShop && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">{selectedShop.name}</h2>
                <p className="text-gray-600">Shop Details & Analytics</p>
              </div>
              <button
                onClick={() => setShowShopDetails(false)}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
              >
                <XCircle className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              {loadingShopDetails ? (
                <div className="flex items-center justify-center h-64">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue"></div>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Tab Navigation */}
                  <div className="border-b border-gray-200">
                    <nav className="flex space-x-8">
                      {[
                        { key: 'analytics', label: 'Analytics', icon: BarChart3 },
                        { key: 'products', label: 'Products', icon: Package },
                        { key: 'orders', label: 'Orders', icon: ShoppingCart }
                      ].map((tab) => {
                        const Icon = tab.icon
                        return (
                          <button
                            key={tab.key}
                            onClick={() => setActiveTab(tab.key as any)}
                            className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                              activeTab === tab.key
                                ? 'border-primary-blue text-primary-blue'
                                : 'border-transparent text-gray-500 hover:text-gray-700'
                            }`}
                          >
                            <Icon className="h-4 w-4 mr-2" />
                            {tab.label}
                          </button>
                        )
                      })}
                    </nav>
                  </div>

                  {/* Analytics Tab */}
                  {activeTab === 'analytics' && shopAnalytics && (
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Analytics Overview</h3>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
                          <div className="flex items-center">
                            <DollarSign className="h-8 w-8 text-green-600" />
                            <div className="ml-3">
                              <p className="text-sm font-medium text-green-700">Total Earnings</p>
                              <p className="text-2xl font-bold text-green-900">
                                {formatCurrency(shopAnalytics.totalEarnings || 0)}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
                          <div className="flex items-center">
                            <ShoppingCart className="h-8 w-8 text-blue-600" />
                            <div className="ml-3">
                              <p className="text-sm font-medium text-blue-700">Total Orders</p>
                              <p className="text-2xl font-bold text-blue-900">{shopAnalytics.totalOrders || 0}</p>
                            </div>
                          </div>
                        </div>
                        <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4">
                          <div className="flex items-center">
                            <Package className="h-8 w-8 text-purple-600" />
                            <div className="ml-3">
                              <p className="text-sm font-medium text-purple-700">Products</p>
                              <p className="text-2xl font-bold text-purple-900">
                                {shopAnalytics.activeProducts || 0}/{shopAnalytics.totalProducts || 0}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-4">
                          <div className="flex items-center">
                            <TrendingUp className="h-8 w-8 text-yellow-600" />
                            <div className="ml-3">
                              <p className="text-sm font-medium text-yellow-700">Monthly Earnings</p>
                              <p className="text-2xl font-bold text-yellow-900">
                                {formatCurrency(shopAnalytics.monthlyEarnings || 0)}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="bg-gradient-to-r from-indigo-50 to-indigo-100 rounded-lg p-4">
                          <div className="flex items-center">
                            <BarChart3 className="h-8 w-8 text-indigo-600" />
                            <div className="ml-3">
                              <p className="text-sm font-medium text-indigo-700">Monthly Orders</p>
                              <p className="text-2xl font-bold text-indigo-900">{shopAnalytics.monthlyOrders || 0}</p>
                            </div>
                          </div>
                        </div>
                        <div className="bg-gradient-to-r from-pink-50 to-pink-100 rounded-lg p-4">
                          <div className="flex items-center">
                            <Star className="h-8 w-8 text-pink-600" />
                            <div className="ml-3">
                              <p className="text-sm font-medium text-pink-700">Rating</p>
                              <p className="text-2xl font-bold text-pink-900">
                                {(selectedShop?.rating || 0).toFixed(1)} ★
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Additional Analytics */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                        {/* Recent Orders */}
                        <div className="bg-white border border-gray-200 rounded-lg p-4">
                          <h4 className="text-md font-semibold text-gray-900 mb-3">Recent Orders</h4>
                          {shopAnalytics.recentOrders && Array.isArray(shopAnalytics.recentOrders) && shopAnalytics.recentOrders.length > 0 ? (
                            <div className="space-y-2">
                              {shopAnalytics.recentOrders.slice(0, 5).map((order, index) => (
                                <div key={order.id || index} className="flex justify-between items-center text-sm">
                                  <div>
                                    <span className="font-medium">{order.order_number || 'N/A'}</span>
                                    <span className="text-gray-500 ml-2">{order.buyer_name || 'Unknown'}</span>
                                  </div>
                                  <div className="text-right">
                                    <div className="font-medium">{formatCurrency(order.total_amount || 0)}</div>
                                    <div className={`text-xs px-2 py-1 rounded-full ${
                                      order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                                      order.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                                      'bg-yellow-100 text-yellow-800'
                                    }`}>
                                      {order.status || 'pending'}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p className="text-gray-500 text-sm">No recent orders</p>
                          )}
                        </div>

                        {/* Top Products */}
                        <div className="bg-white border border-gray-200 rounded-lg p-4">
                          <h4 className="text-md font-semibold text-gray-900 mb-3">Top Selling Products</h4>
                          {shopAnalytics.topSellingProducts && Array.isArray(shopAnalytics.topSellingProducts) && shopAnalytics.topSellingProducts.length > 0 ? (
                            <div className="space-y-2">
                              {shopAnalytics.topSellingProducts.slice(0, 5).map((product, index) => (
                                <div key={product.id || index} className="flex justify-between items-center text-sm">
                                  <div className="flex items-center">
                                    <span className="w-6 h-6 bg-primary-blue text-white rounded-full flex items-center justify-center text-xs mr-2">
                                      {index + 1}
                                    </span>
                                    <span className="font-medium truncate">{product.title}</span>
                                  </div>
                                  <div className="text-right">
                                    <div className="font-medium">{product.totalSold} sold</div>
                                    <div className="text-xs text-gray-500">{formatCurrency(product.revenue)}</div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p className="text-gray-500 text-sm">No sales data available</p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Products Tab */}
                  {activeTab === 'products' && (
                    <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      Shop Products ({shopProducts.length})
                    </h3>
                    {shopProducts.length === 0 ? (
                      <div className="text-center py-8 bg-gray-50 rounded-lg">
                        <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-600">No products found</p>
                      </div>
                    ) : (
                      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                        <div className="divide-y divide-gray-200">
                          {shopProducts.map((product) => (
                            <div key={product.id} className="p-4 hover:bg-gray-50">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4">
                                  <div className="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden">
                                    {product.images && product.images.length > 0 ? (
                                      <img
                                        src={product.images[0].image_url}
                                        alt={product.title}
                                        className="w-full h-full object-cover"
                                      />
                                    ) : (
                                      <div className="w-full h-full flex items-center justify-center">
                                        <Package className="h-6 w-6 text-gray-400" />
                                      </div>
                                    )}
                                  </div>
                                  <div>
                                    <h4 className="font-medium text-gray-900">{product.title}</h4>
                                    <p className="text-sm text-gray-600 line-clamp-1">
                                      {product.description}
                                    </p>
                                    <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                                      <span>Stock: {product.stock_quantity}</span>
                                      <span>•</span>
                                      <span>{formatCurrency(product.price)}</span>
                                      <span>•</span>
                                      <span className="capitalize">{product.condition}</span>
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-3">
                                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                    product.status === 'active'
                                      ? 'bg-green-100 text-green-800'
                                      : product.status === 'inactive'
                                      ? 'bg-gray-100 text-gray-800'
                                      : product.status === 'out_of_stock'
                                      ? 'bg-red-100 text-red-800'
                                      : 'bg-yellow-100 text-yellow-800'
                                  }`}>
                                    {product.status.replace('_', ' ')}
                                  </span>
                                  <button
                                    onClick={() => handleToggleProductStatus(product.id, product.status)}
                                    className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                                      product.status === 'active'
                                        ? 'bg-red-100 text-red-700 hover:bg-red-200'
                                        : 'bg-green-100 text-green-700 hover:bg-green-200'
                                    }`}
                                  >
                                    {product.status === 'active' ? 'Disable' : 'Enable'}
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    </div>
                  )}

                  {/* Orders Tab */}
                  {activeTab === 'orders' && (
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">
                        Shop Orders ({shopOrders.length})
                      </h3>
                      {shopOrders.length === 0 ? (
                        <div className="text-center py-8 bg-gray-50 rounded-lg">
                          <ShoppingCart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                          <p className="text-gray-600">No orders found</p>
                        </div>
                      ) : (
                        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                          <div className="divide-y divide-gray-200">
                            {shopOrders.map((order) => (
                              <div key={order.id} className="p-4 hover:bg-gray-50">
                                <div className="flex items-center justify-between">
                                  <div className="flex-1">
                                    <div className="flex items-center justify-between mb-2">
                                      <h4 className="font-medium text-gray-900">
                                        Order #{order.order_number}
                                      </h4>
                                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                        order.status === 'delivered'
                                          ? 'bg-green-100 text-green-800'
                                          : order.status === 'cancelled'
                                          ? 'bg-red-100 text-red-800'
                                          : order.status === 'shipped'
                                          ? 'bg-blue-100 text-blue-800'
                                          : 'bg-yellow-100 text-yellow-800'
                                      }`}>
                                        {order.status.replace('_', ' ')}
                                      </span>
                                    </div>
                                    <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                                      <div>
                                        <p><span className="font-medium">Buyer:</span> {order.buyer?.full_name || 'Unknown'}</p>
                                        <p><span className="font-medium">Email:</span> {order.buyer?.email || 'N/A'}</p>
                                      </div>
                                      <div>
                                        <p><span className="font-medium">Total:</span> {formatCurrency(order.total_amount)}</p>
                                        <p><span className="font-medium">Date:</span> {new Date(order.created_at).toLocaleDateString()}</p>
                                      </div>
                                    </div>
                                    {order.order_items && order.order_items.length > 0 && (
                                      <div className="mt-3">
                                        <p className="text-sm font-medium text-gray-700 mb-2">Items:</p>
                                        <div className="space-y-1">
                                          {order.order_items.map((item) => (
                                            <div key={item.id} className="flex items-center justify-between text-sm text-gray-600">
                                              <span>{item.product_title} × {item.quantity}</span>
                                              <span>{formatCurrency(item.total_price)}</span>
                                            </div>
                                          ))}
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  )
}

export default function AdminShops() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue"></div>
      </div>
    }>
      <AdminShopsContent />
    </Suspense>
  )
}
