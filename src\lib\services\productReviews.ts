import { supabase, TABLES } from '@/lib/supabase'
import { ProductReview } from '@/types'
import { VendorShopService } from './vendorShops'

export class ProductReviewService {
  /**
   * Add a review for a product
   */
  static async addProductReview(reviewData: Omit<ProductReview, 'id' | 'created_at' | 'updated_at' | 'helpful_count'>): Promise<ProductReview> {
    const { data, error } = await supabase
      .from(TABLES.PRODUCT_REVIEWS)
      .insert({
        ...reviewData,
        helpful_count: 0
      })
      .select(`
        *,
        user:${TABLES.USERS}!product_reviews_user_id_fkey(id, full_name, avatar_url),
        product:${TABLES.SHOP_PRODUCTS}!product_reviews_product_id_fkey(id, shop_id)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to add product review: ${error.message}`)
    }

    // Manually refresh shop statistics as a fallback in case triggers don't work
    try {
      if (data.product?.shop_id) {
        await VendorShopService.refreshShopStatistics(data.product.shop_id)
      }
    } catch (refreshError) {
      console.warn('Failed to refresh shop statistics after adding review:', refreshError)
      // Don't throw error as the review was successfully added
    }

    return data
  }

  /**
   * Get reviews for a product
   */
  static async getProductReviews(
    productId: string, 
    page: number = 1, 
    limit: number = 10
  ): Promise<{
    reviews: ProductReview[]
    total: number
    averageRating: number
  }> {
    const offset = (page - 1) * limit

    const [dataResult, countResult, statsResult] = await Promise.all([
      supabase
        .from(TABLES.PRODUCT_REVIEWS)
        .select(`
          *,
          user:${TABLES.USERS}!product_reviews_user_id_fkey(id, full_name, avatar_url)
        `)
        .eq('product_id', productId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1),
      supabase
        .from(TABLES.PRODUCT_REVIEWS)
        .select('*', { count: 'exact', head: true })
        .eq('product_id', productId),
      supabase
        .from(TABLES.PRODUCT_REVIEWS)
        .select('rating')
        .eq('product_id', productId)
    ])

    if (dataResult.error) {
      throw new Error(`Failed to fetch product reviews: ${dataResult.error.message}`)
    }

    // Calculate average rating
    const ratings = statsResult.data || []
    const averageRating = ratings.length > 0 
      ? ratings.reduce((sum, review) => sum + review.rating, 0) / ratings.length 
      : 0

    return {
      reviews: dataResult.data || [],
      total: countResult.count || 0,
      averageRating: Math.round(averageRating * 100) / 100
    }
  }

  /**
   * Update a product review
   */
  static async updateProductReview(
    reviewId: string, 
    updates: Partial<Pick<ProductReview, 'rating' | 'review_text'>>
  ): Promise<ProductReview> {
    const { data, error } = await supabase
      .from(TABLES.PRODUCT_REVIEWS)
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', reviewId)
      .select(`
        *,
        user:${TABLES.USERS}!product_reviews_user_id_fkey(id, full_name, avatar_url)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to update product review: ${error.message}`)
    }

    return data
  }

  /**
   * Delete a product review
   */
  static async deleteProductReview(reviewId: string): Promise<void> {
    const { error } = await supabase
      .from(TABLES.PRODUCT_REVIEWS)
      .delete()
      .eq('id', reviewId)

    if (error) {
      throw new Error(`Failed to delete product review: ${error.message}`)
    }
  }

  /**
   * Check if user has reviewed a product
   */
  static async hasUserReviewedProduct(userId: string, productId: string): Promise<boolean> {
    const { data, error } = await supabase
      .from(TABLES.PRODUCT_REVIEWS)
      .select('id')
      .eq('user_id', userId)
      .eq('product_id', productId)
      .single()

    if (error && error.code !== 'PGRST116') {
      throw new Error(`Failed to check user review: ${error.message}`)
    }

    return !!data
  }

  /**
   * Get user's review for a product
   */
  static async getUserProductReview(userId: string, productId: string): Promise<ProductReview | null> {
    const { data, error } = await supabase
      .from(TABLES.PRODUCT_REVIEWS)
      .select(`
        *,
        user:${TABLES.USERS}!product_reviews_user_id_fkey(id, full_name, avatar_url)
      `)
      .eq('user_id', userId)
      .eq('product_id', productId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      throw new Error(`Failed to fetch user review: ${error.message}`)
    }

    return data
  }

  /**
   * Get product review statistics
   */
  static async getProductReviewStats(productId: string): Promise<{
    totalReviews: number
    averageRating: number
    ratingDistribution: { [key: number]: number }
  }> {
    const { data, error } = await supabase
      .from(TABLES.PRODUCT_REVIEWS)
      .select('rating')
      .eq('product_id', productId)

    if (error) {
      throw new Error(`Failed to fetch review stats: ${error.message}`)
    }

    const reviews = data || []
    const totalReviews = reviews.length
    const averageRating = totalReviews > 0 
      ? reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews 
      : 0

    // Calculate rating distribution
    const ratingDistribution: { [key: number]: number } = {
      1: 0, 2: 0, 3: 0, 4: 0, 5: 0
    }

    reviews.forEach(review => {
      ratingDistribution[review.rating] = (ratingDistribution[review.rating] || 0) + 1
    })

    return {
      totalReviews,
      averageRating: Math.round(averageRating * 100) / 100,
      ratingDistribution
    }
  }

  /**
   * Get recent reviews for a shop (across all products)
   */
  static async getShopRecentReviews(
    shopId: string, 
    limit: number = 5
  ): Promise<ProductReview[]> {
    const { data, error } = await supabase
      .from(TABLES.PRODUCT_REVIEWS)
      .select(`
        *,
        user:${TABLES.USERS}!product_reviews_user_id_fkey(id, full_name, avatar_url),
        product:${TABLES.SHOP_PRODUCTS}!product_reviews_product_id_fkey(
          id,
          ad:${TABLES.ADS}(id, title)
        )
      `)
      .eq('product.shop_id', shopId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      throw new Error(`Failed to fetch shop reviews: ${error.message}`)
    }

    return data || []
  }
}
