import Image from 'next/image'
import Link from 'next/link'
import { cn } from '@/lib/utils'

interface LogoProps {
  /** Size variant for the logo */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  /** Whether to show the logo as a link to home page */
  linkToHome?: boolean
  /** Additional CSS classes */
  className?: string
  /** Whether to show text alongside the logo */
  showText?: boolean
  /** Custom text to display (defaults to "OKDOI") */
  text?: string
  /** Text styling variant */
  textVariant?: 'default' | 'gradient' | 'white' | 'dark'
  /** Logo orientation */
  orientation?: 'horizontal' | 'vertical'
  /** Priority loading for above-the-fold logos */
  priority?: boolean
}

const sizeConfig = {
  xs: { width: 40, height: 40, textSize: 'text-lg' },
  sm: { width: 60, height: 60, textSize: 'text-xl' },
  md: { width: 80, height: 80, textSize: 'text-2xl' },
  lg: { width: 200, height: 80, textSize: 'text-3xl' }, // Much larger for header logo
  xl: { width: 120, height: 120, textSize: 'text-4xl' },
  '2xl': { width: 150, height: 150, textSize: 'text-5xl' }
}

const textVariants = {
  default: 'text-gray-900',
  gradient: 'bg-gradient-to-r from-primary-blue to-secondary-blue bg-clip-text text-transparent',
  white: 'text-white',
  dark: 'text-gray-900'
}

export default function Logo({
  size = 'md',
  linkToHome = false,
  className,
  showText = false, // Default to false - only show logo image
  text = 'OKDOI',
  textVariant = 'default',
  orientation = 'horizontal',
  priority = false
}: LogoProps) {
  const config = sizeConfig[size]

  const logoImage = (
    <Image
      src="/images/logo.png"
      alt="OKDOI Logo"
      width={config.width}
      height={config.height}
      className={cn(
        'object-contain',
        className
      )}
      priority={priority}
    />
  )

  const logoText = showText && (
    <span className={cn(
      'font-bold font-display tracking-tight',
      config.textSize,
      textVariants[textVariant]
    )}>
      {text}
    </span>
  )

  const logoContent = showText ? (
    <div className={cn(
      'flex items-center',
      orientation === 'horizontal' ? 'space-x-3' : 'flex-col space-y-2'
    )}>
      {logoImage}
      {logoText}
    </div>
  ) : logoImage

  if (linkToHome) {
    return (
      <Link href="/" className="flex items-center hover:opacity-80 transition-opacity">
        {logoContent}
      </Link>
    )
  }

  return logoContent
}

// Preset logo components for common use cases
export function HeaderLogo({ className, ...props }: Omit<LogoProps, 'size' | 'linkToHome' | 'priority' | 'showText'>) {
  return (
    <Logo
      size="lg"
      linkToHome={true}
      priority={true}
      showText={false}
      className={cn('h-16 w-auto', className)} // Larger height for header visibility
      {...props}
    />
  )
}

export function FooterLogo({ className, ...props }: Omit<LogoProps, 'size' | 'showText'>) {
  return (
    <Logo
      size="md"
      showText={false}
      className={cn('h-10 w-auto', className)}
      {...props}
    />
  )
}

export function AdminLogo({ className, ...props }: Omit<LogoProps, 'size' | 'showText'>) {
  return (
    <Logo
      size="md"
      showText={false}
      className={cn('h-10 w-auto', className)}
      {...props}
    />
  )
}

export function DashboardLogo({ className, ...props }: Omit<LogoProps, 'size' | 'linkToHome' | 'showText'>) {
  return (
    <Logo
      size="lg"
      linkToHome={true}
      showText={false}
      className={cn('h-16 w-auto', className)}
      {...props}
    />
  )
}

export function AuthLogo({ className, ...props }: Omit<LogoProps, 'size' | 'showText'>) {
  return (
    <Logo
      size="xl"
      showText={false}
      className={cn('h-20 w-auto', className)}
      {...props}
    />
  )
}

export function LoadingLogo({ className, ...props }: Omit<LogoProps, 'size' | 'showText'>) {
  return (
    <Logo
      size="2xl"
      showText={false}
      className={cn('h-24 w-auto', className)}
      {...props}
    />
  )
}
